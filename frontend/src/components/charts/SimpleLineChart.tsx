/**
 * Simple Line Chart component using SVG
 */

interface ChartDataPoint {
  label: string
  value: number
}

interface SimpleLineChartProps {
  data: ChartDataPoint[]
  title?: string
  height?: number
  width?: number
  color?: string
  showDots?: boolean
  showGrid?: boolean
}

export default function SimpleLineChart({ 
  data, 
  title, 
  height = 200, 
  width = 400,
  color = '#3B82F6',
  showDots = true,
  showGrid = true
}: SimpleLineChartProps) {
  if (data.length === 0) {
    return (
      <div className="w-full">
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <div className="flex items-center justify-center h-48 text-gray-500">
          No data available
        </div>
      </div>
    )
  }

  const padding = 40
  const chartWidth = width - (padding * 2)
  const chartHeight = height - (padding * 2)
  
  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const valueRange = maxValue - minValue || 1

  // Calculate points
  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight
    return { x, y, ...point }
  })

  // Create path string
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L'
    return `${path} ${command} ${point.x} ${point.y}`
  }, '')

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      <div className="relative">
        <svg width={width} height={height} className="overflow-visible">
          {/* Grid lines */}
          {showGrid && (
            <g className="opacity-20">
              {/* Horizontal grid lines */}
              {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
                const y = padding + chartHeight - (ratio * chartHeight)
                return (
                  <line
                    key={`h-${ratio}`}
                    x1={padding}
                    y1={y}
                    x2={padding + chartWidth}
                    y2={y}
                    stroke="#6B7280"
                    strokeWidth="1"
                  />
                )
              })}
              
              {/* Vertical grid lines */}
              {points.map((point, index) => (
                <line
                  key={`v-${index}`}
                  x1={point.x}
                  y1={padding}
                  x2={point.x}
                  y2={padding + chartHeight}
                  stroke="#6B7280"
                  strokeWidth="1"
                />
              ))}
            </g>
          )}
          
          {/* Line */}
          <path
            d={pathData}
            fill="none"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {showDots && points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={color}
              className="hover:r-6 transition-all duration-200"
            />
          ))}
          
          {/* Y-axis labels */}
          <g className="text-xs fill-gray-500">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
              const y = padding + chartHeight - (ratio * chartHeight)
              const value = minValue + (ratio * valueRange)
              return (
                <text
                  key={`y-${ratio}`}
                  x={padding - 10}
                  y={y + 4}
                  textAnchor="end"
                  className="text-xs"
                >
                  {Math.round(value)}
                </text>
              )
            })}
          </g>
          
          {/* X-axis labels */}
          <g className="text-xs fill-gray-500">
            {points.map((point, index) => (
              <text
                key={`x-${index}`}
                x={point.x}
                y={height - 10}
                textAnchor="middle"
                className="text-xs"
              >
                {point.label}
              </text>
            ))}
          </g>
        </svg>
      </div>
    </div>
  )
}
