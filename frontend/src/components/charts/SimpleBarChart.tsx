/**
 * Simple Bar Chart component using CSS
 */

interface ChartData {
  label: string
  value: number
  color?: string
}

interface SimpleBarChartProps {
  data: ChartData[]
  title?: string
  height?: number
  showValues?: boolean
}

export default function SimpleBarChart({ 
  data, 
  title, 
  height = 200, 
  showValues = true 
}: SimpleBarChartProps) {
  const maxValue = Math.max(...data.map(d => d.value))
  
  const defaultColors = [
    '#3B82F6', // blue-500
    '#10B981', // emerald-500
    '#F59E0B', // amber-500
    '#EF4444', // red-500
    '#8B5CF6', // violet-500
    '#06B6D4', // cyan-500
  ]

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      <div className="flex items-end space-x-2" style={{ height: `${height}px` }}>
        {data.map((item, index) => {
          const barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 40) : 0
          const color = item.color || defaultColors[index % defaultColors.length]
          
          return (
            <div key={item.label} className="flex-1 flex flex-col items-center">
              <div className="flex flex-col items-center justify-end h-full">
                {showValues && (
                  <span className="text-xs font-medium text-gray-600 mb-1">
                    {item.value}
                  </span>
                )}
                <div
                  className="w-full rounded-t transition-all duration-300 hover:opacity-80"
                  style={{
                    height: `${barHeight}px`,
                    backgroundColor: color,
                    minHeight: item.value > 0 ? '4px' : '0px',
                  }}
                />
              </div>
              <div className="mt-2 text-xs text-gray-500 text-center">
                {item.label}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
