/**
 * Collapsible sidebar navigation component
 */

import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  BarChart3,
  Package,
  Users,
  Truck,
  Archive,
  Ship,
  Settings,
  ChevronDown,
  ChevronRight,
  Plus,
  List,
  Activity,
  MapPin,
  TrendingUp,
  AlertTriangle,
} from 'lucide-react'
import { cn } from '../../lib/utils'

interface SidebarProps {
  open: boolean
  setOpen: (open: boolean) => void
}

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  children?: NavItem[]
}

const navigation: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: BarChart3,
  },
  {
    name: 'Picks',
    href: '/picks',
    icon: Package,
    children: [
      { name: 'Dashboard', href: '/picks/dashboard', icon: BarChart3 },
      { name: 'Create Pick', href: '/picks/create', icon: Plus },
      { name: 'Pick List', href: '/picks/list', icon: List },
    ],
  },
  {
    name: 'Operators',
    href: '/operators',
    icon: Users,
    children: [
      { name: 'Performance Dashboard', href: '/operators/dashboard', icon: BarChart3 },
      { name: 'Operator List', href: '/operators/list', icon: List },
      { name: 'Activity Logs', href: '/operators/activity', icon: Activity },
    ],
  },
  {
    name: 'Vehicles',
    href: '/vehicles',
    icon: Truck,
    children: [
      { name: 'Movement Tracking', href: '/vehicles/tracking', icon: MapPin },
      { name: 'Vehicle List', href: '/vehicles/list', icon: List },
      { name: 'Efficiency Metrics', href: '/vehicles/metrics', icon: TrendingUp },
    ],
  },
  {
    name: 'Inventory',
    href: '/inventory',
    icon: Archive,
    children: [
      { name: 'Transaction Dashboard', href: '/inventory/dashboard', icon: BarChart3 },
      { name: 'New Transaction', href: '/inventory/create', icon: Plus },
      { name: 'Transaction History', href: '/inventory/history', icon: List },
    ],
  },
  {
    name: 'Shipments',
    href: '/shipments',
    icon: Ship,
    children: [
      { name: 'Shipment Dashboard', href: '/shipments/dashboard', icon: BarChart3 },
      { name: 'Create Shipment', href: '/shipments/create', icon: Plus },
      { name: 'Shipment List', href: '/shipments/list', icon: List },
    ],
  },
  {
    name: 'System',
    href: '/system',
    icon: Settings,
    children: [
      { name: 'Failed Messages', href: '/system/failed-messages', icon: AlertTriangle },
      { name: 'Settings', href: '/system/settings', icon: Settings },
    ],
  },
]

export default function Sidebar({ open, setOpen }: SidebarProps) {
  const location = useLocation()
  const [expandedItems, setExpandedItems] = useState<string[]>(['picks', 'operators'])

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/')
  }

  const isExpanded = (itemName: string) => {
    return expandedItems.includes(itemName.toLowerCase())
  }

  return (
    <aside
      id="sidebar-nav"
      aria-label="Primary"
      className={cn(
        'fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 transition-all duration-300 z-40 md:translate-x-0',
        open ? 'translate-x-0 w-64 md:w-64' : '-translate-x-full w-64 md:w-16'
      )}
    >
      <nav className="h-full overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navigation.map((item) => (
            <li key={item.name}>
              {/* Main navigation item */}
              <div
                className={cn(
                  'flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                  isActive(item.href)
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <Link
                  to={item.href}
                  className="flex items-center space-x-3 flex-1"
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  {open && <span>{item.name}</span>}
                </Link>
                
                {/* Expand/collapse button for items with children */}
                {item.children && open && (
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className="p-1 rounded hover:bg-gray-200"
                    aria-label={`Toggle ${item.name} submenu`}
                    aria-expanded={isExpanded(item.name)}
                    aria-controls={`submenu-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                  >
                    {isExpanded(item.name) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                )}
              </div>

              {/* Sub-navigation items */}
              {item.children && open && isExpanded(item.name) && (
                <ul
                  id={`submenu-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                  className="mt-1 space-y-1 pl-6"
                >
                  {item.children.map((child) => (
                    <li key={child.name}>
                      <Link
                        to={child.href}
                        aria-current={isActive(child.href) ? 'page' : undefined}
                        className={cn(
                          'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm transition-colors',
                          isActive(child.href)
                            ? 'bg-blue-50 text-blue-700 font-medium'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        )}
                      >
                        <child.icon className="h-4 w-4 flex-shrink-0" />
                        <span>{child.name}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  )
}
