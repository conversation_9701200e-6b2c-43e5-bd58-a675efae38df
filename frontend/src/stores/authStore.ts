/**
 * Authentication store using Zustand for state management.
 * 
 * This store manages user authentication state, login/logout functionality,
 * and token management for the application.
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/lib/api';

export interface User {
  id: number;
  email: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  full_name?: string;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
}

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post<AuthToken>('/auth/login', credentials);
          const { access_token } = response;

          // Set token in store and API client
          set({ token: access_token });
          apiClient.setAuthToken(access_token);

          // Get user information
          await get().getCurrentUser();

          set({ 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || 'Login failed';
          set({ 
            isLoading: false, 
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null
          });
          apiClient.clearAuthToken();
          throw error;
        }
      },

      signup: async (data: SignupData) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post<User>('/auth/signup', data);
          
          // After successful signup, automatically log in
          await get().login({ email: data.email, password: data.password });

          set({ isLoading: false, error: null });
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || 'Signup failed';
          set({ 
            isLoading: false, 
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null
          });
          throw error;
        }
      },

      logout: () => {
        // Clear token from API client
        apiClient.clearAuthToken();
        
        // Reset state
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });

        // Call logout endpoint (fire and forget)
        apiClient.post('/auth/logout').catch(() => {
          // Ignore errors on logout endpoint
        });
      },

      refreshToken: async () => {
        try {
          const { token } = get();
          if (!token) {
            throw new Error('No token available');
          }

          const response = await apiClient.post<AuthToken>('/auth/refresh');
          const { access_token } = response;

          // Update token
          set({ token: access_token });
          apiClient.setAuthToken(access_token);
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      getCurrentUser: async () => {
        try {
          const response = await apiClient.get<User>('/auth/me');
          set({ user: response });
        } catch (error) {
          // If getting user fails, logout
          get().logout();
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // When rehydrating from storage, set token in API client
        if (state?.token) {
          apiClient.setAuthToken(state.token);
        }
      },
    }
  )
);

// Initialize auth state on app start
export const initializeAuth = async () => {
  const { token, getCurrentUser, logout } = useAuthStore.getState();
  
  if (token) {
    try {
      // Verify token is still valid by getting current user
      await getCurrentUser();
    } catch (error) {
      // Token is invalid, logout user
      logout();
    }
  }
};
